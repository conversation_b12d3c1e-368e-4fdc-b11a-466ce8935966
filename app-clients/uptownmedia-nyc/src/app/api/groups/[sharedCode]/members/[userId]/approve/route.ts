import { GroupMembershipServices } from "@/app/api/group-memberships/services/groupMembershipsServices";
import { GroupServices } from "@/app/api/groups/services/groupsServices";

const groupMembershipServices = new GroupMembershipServices();
const groupServices = new GroupServices();

export async function POST(
	request: Request,
	props: { params: Promise<{ sharedCode: string; userId: string }> },
) {
	const params = await props.params;
	const group = await groupServices.getGroupBySharePassCode(params.sharedCode);

	await groupMembershipServices.approveGroupMembershipById(
		group.getGroupId(),
		params.userId,
	);

	return Response.json({ msg: "member approved" }, { status: 200 });
}

// export async function DELETE(
//   request: Request,
//   props: { params: Promise<{ sharedCode: string; userId: string }> },
// ) {
//   const params = await props.params;
//   const group = await groupServices.getGroupBySharePassCode(params.sharedCode);
//
//   await groupMembershipServices.deleteGroupMembership(
//     group.getGroupId(),
//     params.userId,
//   );
//
//   return Response.json({ msg: "member rejected" }, { status: 200 });
// }
